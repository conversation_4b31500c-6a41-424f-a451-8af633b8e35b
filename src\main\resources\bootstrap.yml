spring:
  profiles:
    active: local #指定激活哪个环境配置，激活后，第一个文档内容失效;不指定时，以第一个文档为准
  application:
    name: cloud-gateway-service
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: dev
  # 配置中心
  cloud:
    config:
      uri: http://localhost:7005/
      fail-fast: true
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: test
  # 配置中心
  cloud:
    config:
      uri: http://localhost:7005/
      fail-fast: true
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: nacos
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  # 配置中心
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        namespace: ep-dev
        # 组名也需要加上
        group: TDS_EP_NACOS_GROUP
        #配置文件类型，目前只支持 properties 和 yaml 类型，默认为 properties
        file-extension: yaml
        shared-configs[0]:
          data-id: tds-global-config.yml # 配置文件名-Data Id
          group: TDS_EP_NACOS_GROUP   # 默认为DEFAULT_GROUP
--- #"---"用于分隔不同的profiles（）文档块
spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  profiles:
    on-profile: local
  sleuth:
    web:
      client:
        enabled: true
    sampler:
      probability: 1.0
    reactor:
      instrumentation-type: decorate_on_each
  codec:
    max-in-memory-size: 10MB
  servlet:
    multipart:
      enabled: true
      max-file-size: 150MB
      max-request-size: 150MB
  session:
    store-type: redis
    redis:
      flush-mode: immediate
  # Redis连接池配置
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
      shutdown-timeout: 100ms
    timeout: 3000ms
  main:
    allow-bean-definition-overriding: true
  cloud:
    discovery:
      reactive:
        enabled: false
    gateway:
      routes:
        - id: cloud-gateway-admin
          uri: lb://cloud-gateway-admin
          predicates:
            - Path=/gateway-admin/**
          filters:
            - StripPrefix=1
server:
  tomcat:
    # 优化连接数和线程数，防止内存溢出
    max-connections: 8000
    max-threads: 200
    min-spare-threads: 10
    # 连接超时配置
    connection-timeout: 20000
    # 限制HTTP头和POST大小
    max-http-header-size: 8192
    max-http-post-size: 10MB
  port: 7004
secure:
  ignore:
    urls:
      - "/actuator/prometheus"
      - "/404.png"
      - "/401.png"
      - "/400.png"
      - "/500.png"
      - "/favicon.ico"
security:
  oauth2:
    client:
      client-id: tdsdev
      grant-type: client_credentials
feign:
  hystrix:
    enabled: true
ribbon:
  ReadTimeout: 300000
  ConnectTimeout: 60000
# 熔断器配置
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: false
        isolation:
          strategy: SEMAPHORE
          semaphore:
            # 调整最大信号量，即超过这个指标就进入熔断或返回异常
            maxConcurrentRequests: 10000
          thread:
            timeoutInMilliseconds: 20000000
    #全局熔断设置，因为默认不生效，通过过滤器指定
    global:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 20000000
    #特殊请求熔断设置，因为默认不生效，通过过滤器指定
    single:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 20000000
jwt:
  token-header: Authorization
  expire:
    base: ${jwt.yc_base.expire:{pc:3600,app:3600}}
    customization: ${jwt.yc_base.expire.cust:{pc:1200,app:7200}}
    kickAble: true
    kickByClientId: true
  pri-key:
    path: jwt/pri.key
  pub-key:
    path: jwt/pub.key
  params: openid  #demo:openid,test
  auth:
    transcode: false
logs:
  enabled: true
  ignoreLogUri: /sys/user/login;/sys/user/modifyPwd
  logstash:
    uris: *************:4567
    host: *************
    port: 4567
  name: TDSZUULDEV
  responseOutLimit: 524288
# feignUrl:
#   gateway-admin: cloud-gateway-admin
feignUrl:
  gateway-admin: cloud-gateway-admin
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,heapdump
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true