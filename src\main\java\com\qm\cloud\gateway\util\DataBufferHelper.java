package com.qm.cloud.gateway.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;

/**
 * DataBuffer辅助工具类，用于安全地管理DataBuffer的生命周期
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
public class DataBufferHelper {

    /**
     * 安全地释放DataBuffer，避免重复释放
     * 
     * @param dataBuffer 要释放的DataBuffer
     */
    public static void safeRelease(DataBuffer dataBuffer) {
        if (dataBuffer != null) {
            try {
                // 检查DataBuffer是否已经被释放
                if (dataBuffer.readableByteCount() >= 0) {
                    DataBufferUtils.release(dataBuffer);
                    log.debug("DataBuffer released successfully");
                }
            } catch (Exception e) {
                // 如果释放时出现异常，记录日志但不抛出异常
                log.warn("Failed to release DataBuffer: {}", e.getMessage());
            }
        }
    }

    /**
     * 安全地读取DataBuffer内容为字节数组
     * 
     * @param dataBuffer 要读取的DataBuffer
     * @return 字节数组，如果读取失败返回空数组
     */
    public static byte[] safeRead(DataBuffer dataBuffer) {
        if (dataBuffer == null) {
            return new byte[0];
        }
        
        try {
            int readableBytes = dataBuffer.readableByteCount();
            if (readableBytes <= 0) {
                return new byte[0];
            }
            
            byte[] content = new byte[readableBytes];
            dataBuffer.read(content);
            return content;
        } catch (Exception e) {
            log.warn("Failed to read DataBuffer content: {}", e.getMessage());
            return new byte[0];
        }
    }

    /**
     * 安全地读取DataBuffer内容为字符串
     * 
     * @param dataBuffer 要读取的DataBuffer
     * @param charset 字符编码
     * @return 字符串内容，如果读取失败返回空字符串
     */
    public static String safeReadAsString(DataBuffer dataBuffer, String charset) {
        byte[] content = safeRead(dataBuffer);
        if (content.length == 0) {
            return "";
        }
        
        try {
            return new String(content, charset);
        } catch (Exception e) {
            log.warn("Failed to convert bytes to string: {}", e.getMessage());
            return "";
        }
    }
}
