package com.qm.cloud.gateway.filter;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.qm.cloud.gateway.constant.GateExceType;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.domain.JwtAuthenticationDataResponse;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.jwt.JwtInfo;
import com.qm.cloud.gateway.service.impl.InitLicenseListenerImpl;
import com.qm.cloud.gateway.util.JwtTokenUtil;
import com.qm.cloud.gateway.util.RestResultResponse;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.RedisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.filter.factory.rewrite.RewriteFunction;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseCookie;
import org.springframework.http.client.reactive.ClientHttpResponse;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR;

@Slf4j
@Component
public class DnyAuthGatewayFilterFactory extends AbstractGatewayFilterFactory<DnyAuthGatewayFilterFactory.Config> {

    private static final String REPBODY_USERID = "userId";
    private final static String REQUEST_CLIENT_ID = "requestClientId";
    @Autowired
    private RedisUtils redisUtils;

    private JwtTokenUtil jwtTokenUtil;
    private RedisTemplate redisTemplate;
    private InitLicenseListenerImpl licenseListener;

    private final List<HttpMessageReader<?>> messageReaders = HandlerStrategies.withDefaults().messageReaders();

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("userIdField", "licenseMarkable", "kickAble", "kickByClientId", "clientIdHead");
    }


    private class DnyRewrite implements RewriteFunction<Object, Object> {
        private Config config;

        public DnyRewrite(Config config) {
            this.config = config;
        }

        @Override
        public Publisher<Object> apply(ServerWebExchange exchange, Object originalBody) {
            String originalBodystr = (String) originalBody;
            log.info("original response=" + originalBody);
            log.debug("original response=" + originalBody);
            RestResultResponse jwtAuth = genJwtAuthentication(originalBodystr, config, exchange.getAttribute(REQUEST_CLIENT_ID));
            String moidifiedBody = JSONUtil.toJsonStr(jwtAuth);
            return Mono.just(moidifiedBody);
        }

    }

    public DnyAuthGatewayFilterFactory(JwtTokenUtil jwtTokenUtil, RedisTemplate redisTemplate,
                                       InitLicenseListenerImpl licenseListener) {
        super(Config.class);
        this.jwtTokenUtil = jwtTokenUtil;
        this.redisTemplate = redisTemplate;
        this.licenseListener = licenseListener;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return new ModifyResponseGatewayFilter(config);
    }

    private class ModifyResponseGatewayFilter implements GatewayFilter, Ordered {
        private final Config config;

        public ModifyResponseGatewayFilter(Config config) {
            this.config = config;
        }

        @Override
        public int getOrder() {
            return NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER - 1;
        }

        private ClientResponse prepareClientResponse(ServerWebExchange exchange, Publisher<? extends DataBuffer> body, HttpHeaders httpHeaders) {
            ClientResponse.Builder builder;
            builder = ClientResponse.create(exchange.getResponse().getStatusCode(), messageReaders);
            return builder.headers(headers -> headers.putAll(httpHeaders)).body(Flux.from(body)).build();
        }


        @Override
        @SuppressWarnings("unchecked")
        public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
            //获取clientid并存入Attributes
            String clientId = exchange.getRequest().getHeaders().getFirst(config.clientIdHead);
            if (clientId != null) {
                exchange.getAttributes().put(REQUEST_CLIENT_ID, clientId);
            }

            ServerHttpResponseDecorator responseDecorator = new ServerHttpResponseDecorator(exchange.getResponse()) {

                @Override
                public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {

                    Class inClass = String.class;
                    Class outClass = String.class;

                    String contentType = exchange.getAttribute(ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR);
                    MediaType originalResponseContentType = MediaType.valueOf(contentType);
                    HttpHeaders httpHeaders = new HttpHeaders();
                    httpHeaders.setContentType(originalResponseContentType);

                    ClientResponse clientResponse = prepareClientResponse(exchange, body, httpHeaders);
                    Mono<Object> modifiedBody = clientResponse.bodyToMono(inClass)
                            .flatMap(originalBody -> new DnyRewrite(config).apply(exchange, originalBody));

                    BodyInserter bodyInserter = BodyInserters.fromPublisher(modifiedBody, outClass);
                    CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange,
                            exchange.getResponse().getHeaders());
                    return bodyInserter.insert(outputMessage, new BodyInserterContext()).then(Mono.defer(() -> {
                        long contentLength1 = getDelegate().getHeaders().getContentLength();
                        Flux<DataBuffer> messageBody = outputMessage.getBody();
                        HttpHeaders headers = getDelegate().getHeaders();
                        if (/* headers.getContentLength() < 0 && */ !headers
                                .containsKey(HttpHeaders.TRANSFER_ENCODING)) {
                            messageBody = messageBody
                                    .doOnNext(data -> headers.setContentLength(data.readableByteCount()));
                        }
                        return getDelegate().writeWith(messageBody);
                    }))
                    .onErrorResume(throwable -> {
                        log.info("---error--"+"执行url" + exchange.getRequest().getURI() + "DnyAuthGatewayFilterFactory-apply-BodyInserter异常：" + throwable.getMessage());
                        return outputMessage.getBody().map(DataBufferUtils::release).then(chain.filter(exchange));
                    });
                }

                @Override
                public Mono<Void> writeAndFlushWith(Publisher<? extends Publisher<? extends DataBuffer>> body) {
                    return writeWith(Flux.from(body).flatMapSequential(p -> p));
                }
            };
            return chain.filter(exchange.mutate().response(responseDecorator).build());
        }
    }

    public class ResponseAdapter implements ClientHttpResponse {

        private final Flux<DataBuffer> flux;
        private final HttpHeaders headers;

        public ResponseAdapter(Publisher<? extends DataBuffer> body, HttpHeaders headers) {
            this.headers = headers;
            if (body instanceof Flux) {
                flux = (Flux) body;
            } else {
                flux = ((Mono) body).flux();
            }
        }

        @Override
        public Flux<DataBuffer> getBody() {
            return flux;
        }

        @Override
        public HttpHeaders getHeaders() {
            return headers;
        }

        @Override
        public HttpStatus getStatusCode() {
            return null;
        }

        @Override
        public int getRawStatusCode() {
            return 0;
        }

        @Override
        public MultiValueMap<String, ResponseCookie> getCookies() {
            return null;
        }
    }

    @Data
    public static class Config {
        // 指定表示用户ID的字段, 默认userId。
        private String userIdField = REPBODY_USERID;
        // 返回的jwt是否带license, 默认不带license。配置方式：DnyAuth 或 DnyAuth=true 或 DnyAuth=false
        private boolean licenseMarkable;
        // 返回的jwt是否互踢，默认互踢。
        private boolean kickAble = true;
        // 是否按照设备id互踢，默认否
        private boolean kickByClientId;
        // clientId所在的header, 默认x-client-id
        private String clientIdHead = "x-client-id";

    }

    public RestResultResponse<Map<String, String>> genJwtAuthentication(String repBody, Config config, String clientId) {
        try {
            String resultCodeKey="resultCode";
            String jwt = "";
            log.debug("-----repBody-----" + repBody);
            JSONObject jsonResp = JSONObject.parseObject(repBody);
            Map<String, String> dataTemp = (Map<String, String>) jsonResp.get("data");
            log.info("--------code------" + jsonResp.get(resultCodeKey).toString());
            if (!"200".equals(jsonResp.get(resultCodeKey).toString())) {
                RestResultResponse<Map<String, String>> bodyFmt = new RestResultResponse<>()
                        .data(new JwtAuthenticationDataResponse(jwt, dataTemp));
                bodyFmt.setErrMsg(jsonResp.get("errMsg").toString());
                bodyFmt.setResultCode(Integer.parseInt(jsonResp.get(resultCodeKey).toString()));
                bodyFmt.setData(null);
                log.info("---error--"+"生成jwt失败, resultCode:{}", jsonResp.get(resultCodeKey).toString());
                return bodyFmt;
            }
            Map<String, String> data = new HashMap<String, String>();
            for (Map.Entry<String, String> entry : dataTemp.entrySet()) {
                data.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
            RestResultResponse<Map<String, String>> bodyFmt = new RestResultResponse<>()
                    .data(new JwtAuthenticationDataResponse(jwt, data));
            String userId = String.valueOf(data.get(config.userIdField));
            if (StringUtils.isEmpty(userId)) {
                log.info("---error--"+"生成jwt失败,{}为空", config.userIdField);
                bodyFmt.setResultCode(GateExceType.USERIDNULL.getType());
                bodyFmt.setData(null);
                bodyFmt.setErrMsg(GateExceType.USERIDNULL.getTypeName());
                return bodyFmt;
            }

            log.info("------config------" + config.toString());
            // 接口返回是否互踢参数
            Boolean kickOut = config.kickAble;
            log.info("------kickOut------" + kickOut);
            // 只有在设置了互踢，按照设备互踢才生效。
            Boolean fixedKickByClientId = config.kickAble && config.kickByClientId;
            // 当按设备互踢时，UniqueName(即userName)=userId+clientId
            String userName = fixedKickByClientId && clientId != null ? userId + GatewayConstants.JWT_CLIENT_DELIMITER + clientId : userId;

            IJwtInfo info = new JwtInfo(userName, userId, null, null, null, null, kickOut, data, "");
            // 直接根据用户信息返回jwt 20210913
            jwt = jwtTokenUtil.generateToken(info, "", "");

            if (config.licenseMarkable) {
                // 返回带license的jwt
                jwt += licenseListener.getLicense().getAuthorizationCode();
            }
            // 根据jwt缓存用户信息
            String loginName = info.getUniqueName();
            long expire = jwtTokenUtil.getExpire(loginName);
            String userType = "[" + clientId + "]";
            String jwtKey = redisUtils.keyBuilder(GatewayConstants.JWT, userType, jwt);
            String loginNameKey = redisUtils.keyBuilder(GatewayConstants.LOGINNAME, userType, loginName);
            log.info("------loginNameKey------" + loginNameKey);
            // 如果设置互踢，把旧jwt从redis中删除
            if (config.kickAble) {
                Object oldjwt = redisTemplate.opsForValue().get(loginNameKey);
                if (null != oldjwt) {
                    redisTemplate.delete(loginNameKey);
                    String oldJwtKey = redisUtils.keyBuilder(GatewayConstants.JWT, userType, oldjwt.toString());
                    redisTemplate.delete(loginNameKey);
                    redisTemplate.delete(oldJwtKey);
                    log.info("从缓存中删除旧jwt:{},loginNameKey:{}", oldJwtKey, loginNameKey);
                }
            }
            log.debug("--genJwtAuthentication userType:{} ,jwt:{} ,jwtKey:{},info:{}", userType, jwt, jwtKey, info);
            redisTemplate.opsForValue().set(jwtKey, info, expire, TimeUnit.SECONDS);
            log.info("--缓存信息jwt," + jwt);
            // 根据用户账号缓存有效的jwt，踢掉其它的jwt

            log.debug("--genJwtAuthentication loginNameKey:{} ,jwt:{} ,expire:{}", loginNameKey, jwt, expire);
            redisTemplate.opsForValue().set(loginNameKey, jwt, expire, TimeUnit.SECONDS);
            log.info("--用登录账号缓存有效的jwt,loginName={},expire={}", loginName, expire);
            // rData中也存入jwt给前端
            return new RestResultResponse().data(new JwtAuthenticationDataResponse(jwt, data))
                    .success(true);
        } catch (
                Exception e) {
            log.info("---error--"+"生成jwt失败", e);
            RestResultResponse resp = new RestResultResponse();
            resp.setResultCode(GateExceType.SYSTEMERROR.getType());
            resp.setErrMsg(GateExceType.SYSTEMERROR.getTypeName());
            return resp;
        }
    }
}