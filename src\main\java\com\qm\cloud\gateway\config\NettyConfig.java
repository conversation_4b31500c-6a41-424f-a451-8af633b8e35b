package com.qm.cloud.gateway.config;

import io.netty.util.ResourceLeakDetector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * Netty配置类，用于配置内存泄漏检测和其他Netty相关设置
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Configuration
@Slf4j
public class NettyConfig {

    /**
     * 在应用启动完成后配置Netty内存泄漏检测
     */
    @EventListener(ApplicationReadyEvent.class)
    public void configureNettyLeakDetection() {
        // 设置Netty内存泄漏检测级别
        // DISABLED: 禁用检测
        // SIMPLE: 简单检测，只报告是否有泄漏
        // ADVANCED: 高级检测，报告泄漏的详细信息
        // PARANOID: 偏执检测，检测所有对象的泄漏
        ResourceLeakDetector.setLevel(ResourceLeakDetector.Level.SIMPLE);
        
        log.info("Netty ResourceLeakDetector level set to: {}", ResourceLeakDetector.getLevel());
        
        // 设置采样率，默认是113，表示每113个对象中检测1个
        // 在生产环境中可以设置更大的值以减少性能影响
        System.setProperty("io.netty.leakDetection.samplingInterval", "1024");
        
        log.info("Netty leak detection sampling interval set to: 1024");
    }
}
